.product-catalog-awareness {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 12px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.awareness-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.connection-status {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.connection-status.connected {
  color: #28a745;
}

.connection-status.disconnected {
  color: #dc3545;
}

.awareness-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.connected-users {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.connected-user {
  display: flex;
  align-items: center;
  padding: 8px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  cursor: default;
}

.connected-user:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.user-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-transform: uppercase;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border: 1px solid #dee2e6;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.user-action {
  font-size: 12px;
  color: #6c757d;
  margin: 0;
  font-weight: 500;
}

.user-activity {
  font-size: 11px;
  color: #adb5bd;
  margin: 0;
}

.no-users {
  text-align: center;
  padding: 16px;
  color: #6c757d;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .product-catalog-awareness {
    padding: 8px;
    margin: 5px 0;
  }
  
  .connected-user {
    padding: 6px;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
  
  .action-indicator {
    width: 16px;
    height: 16px;
    font-size: 9px;
  }
  
  .user-name {
    font-size: 13px;
  }
  
  .user-action {
    font-size: 11px;
  }
  
  .user-activity {
    font-size: 10px;
  }
}

/* Animaciones */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.connected-user {
  animation: fadeIn 0.3s ease-out;
}

/* Estados específicos para diferentes acciones */
.connected-user[data-action="viewing_product"] {
  border-left: 3px solid #17a2b8;
}

.connected-user[data-action="editing_product"] {
  border-left: 3px solid #ffc107;
}

.connected-user[data-action="creating_product"] {
  border-left: 3px solid #28a745;
}

.connected-user[data-action="browsing_catalog"] {
  border-left: 3px solid #6c757d;
}

.connected-user[data-action="idle"] {
  opacity: 0.7;
  border-left: 3px solid #dee2e6;
}

/* Indicadores de estado más específicos */
.user-action[data-type="viewing_product"] {
  color: #17a2b8;
}

.user-action[data-type="editing_product"] {
  color: #ffc107;
}

.user-action[data-type="creating_product"] {
  color: #28a745;
}

.user-action[data-type="browsing_catalog"] {
  color: #6c757d;
}

.user-action[data-type="idle"] {
  color: #adb5bd;
  font-style: italic;
}
