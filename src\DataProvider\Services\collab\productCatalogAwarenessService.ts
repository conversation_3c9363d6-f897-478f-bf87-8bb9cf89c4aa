import type * as Y from "yjs";
import type { WebsocketProvider } from "y-websocket";
import { Awareness } from "y-protocols/awareness";

interface ProductCatalogUserAwareness {
  name: string;
  color: string;
  action?: ProductCatalogUserAction;
}

interface ProductCatalogUserAction {
  type: 'viewing_product' | 'editing_product' | 'creating_product' | 'browsing_catalog' | 'idle';
  productId?: string;
  productName?: string;
  timestamp: string;
  details?: {
    modalType?: 'view' | 'edit' | 'create';
    isInModal?: boolean;
  };
}

const productCatalogAwarenessMap = new Map<string, Awareness>();
const productCatalogAwarenessTimers = new Map<string, NodeJS.Timeout>();

// Configuración para limpieza de awareness
const PRODUCT_CATALOG_AWARENESS_CLEANUP_TIMEOUT = 5 * 60 * 1000; // 5 minutos sin actividad

// Función para programar limpieza automática de awareness
const scheduleProductCatalogAwarenessCleanup = (key: string) => {
  // Cancelar timer anterior si existe
  const existingTimer = productCatalogAwarenessTimers.get(key);
  if (existingTimer) {
    clearTimeout(existingTimer);
  }

  // Programar nueva limpieza
  const timer = setTimeout(() => {
    console.log(`[ProductCatalogAwareness] 🧹 Limpiando awareness inactivo: ${key}`);
    productCatalogAwarenessMap.delete(key);
    productCatalogAwarenessTimers.delete(key);
  }, PRODUCT_CATALOG_AWARENESS_CLEANUP_TIMEOUT);

  productCatalogAwarenessTimers.set(key, timer);
};

export const setupProductCatalogAwareness = (
  projectId: string,
  provider: WebsocketProvider,
  initialUser: { name: string; color: string }
) => {
  const key = `product_catalog:${projectId}`;
  const awareness = provider.awareness;

  awareness.setLocalStateField("productCatalogUser", {
    name: initialUser.name,
    color: initialUser.color,
    action: {
      type: 'browsing_catalog',
      timestamp: new Date().toISOString(),
      details: {
        isInModal: false
      }
    }
  });

  productCatalogAwarenessMap.set(key, awareness);

  // Programar limpieza automática
  scheduleProductCatalogAwarenessCleanup(key);

  console.log(`[ProductCatalogAwareness] ✅ Awareness configurado para ${key}`);
};

export const updateProductCatalogUserAction = (
  projectId: string,
  action: ProductCatalogUserAction
) => {
  const key = `product_catalog:${projectId}`;
  const awareness = productCatalogAwarenessMap.get(key);
  if (awareness) {
    const current = awareness.getLocalState();
    if (current?.productCatalogUser) {
      awareness.setLocalStateField("productCatalogUser", {
        ...current.productCatalogUser,
        action
      });
      // Reprogramar limpieza por actividad
      scheduleProductCatalogAwarenessCleanup(key);
      console.log(`[ProductCatalogAwareness] 📝 Acción actualizada:`, action);
    }
  }
};

export const onProductCatalogAwarenessChange = (
  projectId: string,
  callback: (states: Map<number, any>) => void
) => {
  const key = `product_catalog:${projectId}`;
  const awareness = productCatalogAwarenessMap.get(key);
  if (awareness) {
    const handler = () => {
      const states = awareness.getStates();
      callback(states);
    };

    awareness.on("change", handler);
    return () => {
      awareness.off("change", handler);
    };
  }
  return () => {};
};

export const getProductCatalogAwareness = (projectId: string): Awareness | undefined => {
  const key = `product_catalog:${projectId}`;
  return productCatalogAwarenessMap.get(key);
};

export const destroyProductCatalogAwareness = (projectId: string) => {
  const key = `product_catalog:${projectId}`;

  // Cancelar timer de limpieza si existe
  const timer = productCatalogAwarenessTimers.get(key);
  if (timer) {
    clearTimeout(timer);
    productCatalogAwarenessTimers.delete(key);
  }

  productCatalogAwarenessMap.delete(key);
  console.log(`[ProductCatalogAwareness] 🧹 Awareness destruido manualmente para ${key}`);
};

// Funciones de conveniencia para acciones específicas del Product Catalog
export const setUserViewingProduct = (
  projectId: string,
  productId: string,
  productName: string
) => {
  updateProductCatalogUserAction(projectId, {
    type: 'viewing_product',
    productId,
    productName,
    timestamp: new Date().toISOString(),
    details: {
      modalType: 'view',
      isInModal: true
    }
  });
};

export const setUserEditingProduct = (
  projectId: string,
  productId: string,
  productName: string
) => {
  updateProductCatalogUserAction(projectId, {
    type: 'editing_product',
    productId,
    productName,
    timestamp: new Date().toISOString(),
    details: {
      modalType: 'edit',
      isInModal: true
    }
  });
};

export const setUserCreatingProduct = (
  projectId: string
) => {
  updateProductCatalogUserAction(projectId, {
    type: 'creating_product',
    timestamp: new Date().toISOString(),
    details: {
      modalType: 'create',
      isInModal: true
    }
  });
};

export const setUserBrowsingCatalog = (
  projectId: string
) => {
  updateProductCatalogUserAction(projectId, {
    type: 'browsing_catalog',
    timestamp: new Date().toISOString(),
    details: {
      isInModal: false
    }
  });
};

export const setUserIdleInCatalog = (
  projectId: string
) => {
  updateProductCatalogUserAction(projectId, {
    type: 'idle',
    timestamp: new Date().toISOString(),
    details: {
      isInModal: false
    }
  });
};

// Función para limpiar todos los awareness del Product Catalog
export const cleanupAllProductCatalogAwareness = (): void => {
  console.log(`[ProductCatalogAwareness] 🧹 Limpiando todos los awareness...`);

  // Cancelar todos los timers
  productCatalogAwarenessTimers.forEach((timer) => {
    clearTimeout(timer);
  });

  // Limpiar mapas
  productCatalogAwarenessMap.clear();
  productCatalogAwarenessTimers.clear();

  console.log(`[ProductCatalogAwareness] ✅ Todos los awareness limpiados`);
};

// Función para obtener estadísticas simples de awareness
export const getProductCatalogAwarenessStats = () => {
  return {
    totalAwareness: productCatalogAwarenessMap.size,
    scheduledCleanups: productCatalogAwarenessTimers.size
  };
};
