import * as Y from "yjs";
import { getProjectState, getProjectProvider } from './collaborationService';

/**
 * Servicio para manejar la colaboración de configuraciones en BillOfMaterialsEditor
 * Sincroniza el estado de las configuraciones de productos usando YJS
 */
class ConfigurationsCollaborationService {
  private projectId: string | null = null;
  private configurationsState: Y.Map<any> | null = null;
  private isInitialized: boolean = false;
  private ownOperations: Set<string> = new Set(); // Para trackear operaciones propias
  private lastProcessedTimestamp: number = 0; // Para procesar solo cambios nuevos

  /**
   * Inicializa la sincronización de configuraciones para un proyecto colaborativo
   */
  async initializeConfigurationsSync(projectId: string): Promise<boolean> {
    console.log(`[ConfigurationsCollaboration] 🚀 Inicializando sincronización de configuraciones para proyecto: ${projectId}`);

    // Si ya está inicializado para el mismo proyecto, no hacer nada
    if (this.isInitialized && this.projectId === projectId) {
      console.log(`[ConfigurationsCollaboration] ✅ Ya inicializado para proyecto: ${projectId}`);
      return true;
    }

    // Si está inicializado para otro proyecto, limpiar primero
    if (this.isInitialized && this.projectId !== projectId) {
      console.log(`[ConfigurationsCollaboration] 🧹 Limpiando colaboración anterior (${this.projectId}) antes de inicializar nueva (${projectId})`);
      this.cleanup();
    }

    try {
      // Verificar que el proyecto tenga estado colaborativo
      console.log(`[ConfigurationsCollaboration] 🔍 Obteniendo estado del proyecto...`);
      const projectState = getProjectState(projectId);
      console.log(`[ConfigurationsCollaboration] 📋 Estado del proyecto obtenido:`, !!projectState);

      if (!projectState) {
        console.log(`[ConfigurationsCollaboration] ❌ No se encontró estado colaborativo para proyecto: ${projectId}`);
        console.log(`[ConfigurationsCollaboration] 🔍 Intentando inicializar colaboración base primero...`);

        // Intentar inicializar la colaboración base
        try {
          const { setupProjectSync } = await import('./collaborationService');
          console.log(`[ConfigurationsCollaboration] 📞 Llamando a setupProjectSync...`);
          await setupProjectSync(projectId);

          // Intentar obtener el estado nuevamente
          const newProjectState = getProjectState(projectId);
          console.log(`[ConfigurationsCollaboration] 📋 Estado después de inicialización:`, !!newProjectState);

          if (!newProjectState) {
            console.log(`[ConfigurationsCollaboration] ❌ Aún no se puede obtener estado después de inicialización`);
            return false;
          }

          // Continuar con el nuevo estado
          console.log(`[ConfigurationsCollaboration] ✅ Estado colaborativo inicializado exitosamente`);
        } catch (initError) {
          console.error(`[ConfigurationsCollaboration] ❌ Error inicializando colaboración base:`, initError);
          return false;
        }
      }

      // Verificar que el provider esté conectado
      console.log(`[ConfigurationsCollaboration] 🔍 Obteniendo provider del proyecto...`);
      const provider = getProjectProvider(projectId);
      console.log(`[ConfigurationsCollaboration] 📋 Provider obtenido:`, !!provider, provider?.wsconnected);

      if (!provider) {
        console.log(`[ConfigurationsCollaboration] ❌ No se encontró provider para proyecto: ${projectId}`);
        return false;
      }

      this.projectId = projectId;

      // Obtener el estado final (puede ser el original o el recién inicializado)
      const finalProjectState = getProjectState(projectId);
      if (!finalProjectState) {
        console.log(`[ConfigurationsCollaboration] ❌ No se pudo obtener estado final del proyecto`);
        return false;
      }

      // Obtener o crear el mapa de configuraciones en YJS
      console.log(`[ConfigurationsCollaboration] 🗺️ Obteniendo mapa de configuraciones...`);
      let configurationsMap = finalProjectState.get('productConfigurations') as Y.Map<any>;
      console.log(`[ConfigurationsCollaboration] 📋 Mapa existente:`, !!configurationsMap);

      if (!configurationsMap) {
        console.log(`[ConfigurationsCollaboration] 🆕 Creando nuevo mapa de configuraciones...`);
        configurationsMap = new Y.Map<any>();
        finalProjectState.set('productConfigurations', configurationsMap);
        console.log(`[ConfigurationsCollaboration] 📝 Mapa de configuraciones creado para proyecto: ${projectId}`);
      } else {
        console.log(`[ConfigurationsCollaboration] ♻️ Usando mapa de configuraciones existente`);
      }

      // IMPORTANTE: Asegurar que this.configurationsState apunte al mismo mapa que está en projectState
      this.configurationsState = finalProjectState.get('productConfigurations') as Y.Map<any>;

      // Verificar que ambos mapas son el mismo objeto
      console.log(`[ConfigurationsCollaboration] 🔍 Verificación de referencia:`, {
        configurationsMapSameAsStored: configurationsMap === this.configurationsState,
        bothExist: !!configurationsMap && !!this.configurationsState,
        configurationsMapSize: configurationsMap?.size,
        storedMapSize: this.configurationsState?.size
      });

      this.isInitialized = true;
      console.log(`[ConfigurationsCollaboration] ✅ Sincronización de configuraciones inicializada exitosamente. Estado:`, {
        projectId: this.projectId,
        isInitialized: this.isInitialized,
        hasConfigurationsState: !!this.configurationsState
      });

      return true;
    } catch (error) {
      console.error(`[ConfigurationsCollaboration] ❌ Error inicializando sincronización:`, error);
      return false;
    }
  }

  /**
   * Observa cambios en las configuraciones colaborativas
   */
  observeConfigurationsChanges(callback: (changes: any) => void): (() => void) | null {
    if (!this.isInitialized || !this.configurationsState) {
      console.log(`[ConfigurationsCollaboration] ⚠️ Configurations collaboration no inicializado para observar cambios`);
      return null;
    }

    console.log(`[ConfigurationsCollaboration] 👀 Iniciando observación de cambios en configuraciones`);

    const observer = (event: any) => {
      console.log(`[ConfigurationsCollaboration] 🔔 Cambio detectado en configuraciones:`, event);

      // Obtener todas las operaciones de configuraciones
      const allOperations = this.configurationsState?.toJSON();
      console.log(`[ConfigurationsCollaboration] 📋 Todas las operaciones en YJS:`, allOperations);
      console.log(`[ConfigurationsCollaboration] 🏷️ Operaciones propias:`, Array.from(this.ownOperations));
      console.log(`[ConfigurationsCollaboration] ⏰ Último timestamp procesado:`, this.lastProcessedTimestamp);

      // Filtrar solo las operaciones nuevas (posteriores al último timestamp procesado)
      const newOperations: any = {};
      let hasNewOperations = false;

      if (allOperations) {
        Object.keys(allOperations).forEach(key => {
          const operation = allOperations[key];
          const isNotCurrentConfigs = key !== 'currentConfigurations';
          const hasType = operation.type;
          const isNotOwnOperation = !this.ownOperations.has(key);
          const isNewTimestamp = (operation.timestamp || 0) > this.lastProcessedTimestamp;

          console.log(`[ConfigurationsCollaboration] 🔍 Evaluando operación ${key}:`, {
            isNotCurrentConfigs,
            hasType,
            isNotOwnOperation,
            isNewTimestamp,
            timestamp: operation.timestamp,
            type: operation.type
          });

          if (isNotCurrentConfigs && hasType && isNotOwnOperation && isNewTimestamp) {
            newOperations[key] = operation;
            hasNewOperations = true;
            console.log(`[ConfigurationsCollaboration] ✅ Operación ${key} incluida para procesamiento`);
          }
        });
      }

      console.log(`[ConfigurationsCollaboration] 📊 Resumen de filtrado:`, {
        totalOperations: allOperations ? Object.keys(allOperations).length : 0,
        newOperations: Object.keys(newOperations).length,
        hasNewOperations
      });

      // Solo procesar si hay operaciones nuevas
      if (hasNewOperations) {
        // Actualizar timestamp de última operación procesada
        const timestamps = Object.values(newOperations).map((op: any) => op.timestamp || 0);
        this.lastProcessedTimestamp = Math.max(...timestamps);

        console.log(`[ConfigurationsCollaboration] 📥 Procesando ${Object.keys(newOperations).length} operaciones nuevas`);
        console.log(`[ConfigurationsCollaboration] 📤 Enviando callback con operaciones:`, newOperations);

        callback({
          type: 'configurations-operations',
          data: newOperations,
          event: event
        });
      } else {
        console.log(`[ConfigurationsCollaboration] ⏭️ No hay operaciones nuevas para procesar`);
      }
    };

    this.configurationsState.observe(observer);

    // Retornar función de cleanup
    return () => {
      console.log(`[ConfigurationsCollaboration] 🛑 Deteniendo observación de cambios en configuraciones`);
      this.configurationsState?.unobserve(observer);
    };
  }

  /**
   * Sincroniza una operación de eliminar configuración completa
   */
  syncDeleteConfigurationOperation(deletionData: any): void {
    console.log(`[ConfigurationsCollaboration] 🔍 Verificando estado antes de sincronizar eliminación de configuración...`);
    console.log(`[ConfigurationsCollaboration] 📋 Estado actual:`, {
      isInitialized: this.isInitialized,
      hasConfigurationsState: !!this.configurationsState,
      projectId: this.projectId
    });

    if (!this.isInitialized || !this.configurationsState) {
      console.log(`[ConfigurationsCollaboration] ⚠️ Configurations collaboration no inicializado, no se puede sincronizar eliminación de configuración`);
      return;
    }

    const operation = {
      type: 'DELETE_CONFIGURATION',
      timestamp: Date.now(),
      operationId: `delete_config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      data: deletionData
    };

    console.log(`[ConfigurationsCollaboration] 🗑️ Sincronizando operación DELETE_CONFIGURATION:`, operation);

    // Marcar como operación propia para no procesarla cuando la recibamos
    this.ownOperations.add(operation.operationId);
    console.log(`[ConfigurationsCollaboration] 🏷️ Operación marcada como propia:`, operation.operationId);

    // Agregar la operación a YJS
    try {
      const projectState = getProjectState(this.projectId!);
      const freshConfigurationsMap = projectState?.get('productConfigurations') as Y.Map<any>;
      const targetMap = freshConfigurationsMap || this.configurationsState;

      if (!targetMap) {
        throw new Error('No se encontró mapa de configuraciones válido para eliminación de configuración');
      }

      targetMap.set(operation.operationId, operation);
      console.log(`[ConfigurationsCollaboration] ✅ Operación de eliminación de configuración agregada a YJS exitosamente`);

    } catch (error) {
      console.error(`[ConfigurationsCollaboration] ❌ Error agregando operación de eliminación de configuración a YJS:`, error);
    }
  }

  /**
   * Sincroniza una operación de eliminar producto/funcionalidad
   */
  syncDeleteProductOperation(deletionData: any): void {
    console.log(`[ConfigurationsCollaboration] 🔍 Verificando estado antes de sincronizar eliminación...`);
    console.log(`[ConfigurationsCollaboration] 📋 Estado actual:`, {
      isInitialized: this.isInitialized,
      hasConfigurationsState: !!this.configurationsState,
      projectId: this.projectId
    });

    if (!this.isInitialized || !this.configurationsState) {
      console.log(`[ConfigurationsCollaboration] ⚠️ Configurations collaboration no inicializado, no se puede sincronizar eliminación`);
      console.log(`[ConfigurationsCollaboration] 🔍 Detalles del error:`, {
        isInitialized: this.isInitialized,
        configurationsState: !!this.configurationsState
      });
      return;
    }

    const operation = {
      type: 'DELETE_PRODUCT',
      timestamp: Date.now(),
      operationId: `delete_product_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      data: deletionData
    };

    console.log(`[ConfigurationsCollaboration] 🗑️ Sincronizando operación DELETE_PRODUCT:`, operation);

    // Marcar como operación propia para no procesarla cuando la recibamos
    this.ownOperations.add(operation.operationId);
    console.log(`[ConfigurationsCollaboration] 🏷️ Operación marcada como propia:`, operation.operationId);

    // Agregar la operación a YJS usando la misma lógica que CREATE_PRODUCT
    try {
      // Obtener la referencia fresca del projectState para asegurar consistencia
      const projectState = getProjectState(this.projectId!);
      const freshConfigurationsMap = projectState?.get('productConfigurations') as Y.Map<any>;

      console.log(`[ConfigurationsCollaboration] 🔍 Estado antes de agregar eliminación:`, {
        hasProjectState: !!projectState,
        hasFreshMap: !!freshConfigurationsMap,
        hasStoredMap: !!this.configurationsState,
        freshMapSize: freshConfigurationsMap?.size,
        storedMapSize: this.configurationsState?.size,
        areSameReference: freshConfigurationsMap === this.configurationsState
      });

      // Usar la referencia fresca para asegurar que estamos escribiendo en el mapa correcto
      const targetMap = freshConfigurationsMap || this.configurationsState;
      if (!targetMap) {
        throw new Error('No se encontró mapa de configuraciones válido para eliminación');
      }

      targetMap.set(operation.operationId, operation);
      console.log(`[ConfigurationsCollaboration] ✅ Operación de eliminación agregada a YJS exitosamente`);

      // Verificar que se agregó correctamente
      const verificacionFresh = freshConfigurationsMap?.get(operation.operationId);
      const verificacionStored = this.configurationsState?.get(operation.operationId);

      console.log(`[ConfigurationsCollaboration] 🔍 Verificación - operación de eliminación en YJS:`, {
        enMapaFresco: !!verificacionFresh,
        enMapaAlmacenado: !!verificacionStored,
        ambosIguales: verificacionFresh === verificacionStored
      });

    } catch (error) {
      console.error(`[ConfigurationsCollaboration] ❌ Error agregando operación de eliminación a YJS:`, error);
    }
  }

  /**
   * Sincroniza una operación de crear producto
   */
  syncCreateProductOperation(productData: any): void {
    console.log(`[ConfigurationsCollaboration] 🔍 Verificando estado antes de sincronizar...`);
    console.log(`[ConfigurationsCollaboration] 📋 Estado actual:`, {
      isInitialized: this.isInitialized,
      hasConfigurationsState: !!this.configurationsState,
      projectId: this.projectId
    });

    if (!this.isInitialized || !this.configurationsState) {
      console.log(`[ConfigurationsCollaboration] ⚠️ Configurations collaboration no inicializado, no se puede sincronizar creación de producto`);
      console.log(`[ConfigurationsCollaboration] 🔍 Detalles del error:`, {
        isInitialized: this.isInitialized,
        configurationsState: !!this.configurationsState
      });
      return;
    }

    const operation = {
      type: 'CREATE_PRODUCT',
      timestamp: Date.now(),
      operationId: `create_product_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      data: productData
    };

    console.log(`[ConfigurationsCollaboration] 📤 Sincronizando operación CREATE_PRODUCT:`, operation);

    // Marcar como operación propia para no procesarla cuando la recibamos
    this.ownOperations.add(operation.operationId);
    console.log(`[ConfigurationsCollaboration] 🏷️ Operación marcada como propia:`, operation.operationId);

    // Agregar la operación a YJS
    try {
      // Obtener la referencia fresca del projectState para asegurar consistencia
      const projectState = getProjectState(this.projectId!);
      const freshConfigurationsMap = projectState?.get('productConfigurations') as Y.Map<any>;

      console.log(`[ConfigurationsCollaboration] 🔍 Estado antes de agregar:`, {
        hasProjectState: !!projectState,
        hasFreshMap: !!freshConfigurationsMap,
        hasStoredMap: !!this.configurationsState,
        freshMapSize: freshConfigurationsMap?.size,
        storedMapSize: this.configurationsState?.size,
        areSameReference: freshConfigurationsMap === this.configurationsState
      });

      // Usar la referencia fresca para asegurar que estamos escribiendo en el mapa correcto
      const targetMap = freshConfigurationsMap || this.configurationsState;
      if (!targetMap) {
        throw new Error('No se encontró mapa de configuraciones válido');
      }

      targetMap.set(operation.operationId, operation);
      console.log(`[ConfigurationsCollaboration] ✅ Operación agregada a YJS exitosamente`);

      // Verificar que se agregó correctamente en ambos mapas
      const verificacionFresh = freshConfigurationsMap?.get(operation.operationId);
      const verificacionStored = this.configurationsState?.get(operation.operationId);

      console.log(`[ConfigurationsCollaboration] 🔍 Verificación - operación en YJS:`, {
        enMapaFresco: !!verificacionFresh,
        enMapaAlmacenado: !!verificacionStored,
        ambosIguales: verificacionFresh === verificacionStored
      });

      console.log(`[ConfigurationsCollaboration] 📊 Estado después de agregar:`, {
        freshMapSize: freshConfigurationsMap?.size,
        storedMapSize: this.configurationsState?.size,
        freshMapKeys: freshConfigurationsMap ? Array.from(freshConfigurationsMap.keys()) : [],
        storedMapKeys: this.configurationsState ? Array.from(this.configurationsState.keys()) : []
      });

      // Verificar el estado del proyecto también
      const currentProjectState = getProjectState(this.projectId!);
      if (currentProjectState) {
        const configMap = currentProjectState.get('productConfigurations');
        console.log(`[ConfigurationsCollaboration] 🗺️ Verificación en projectState:`, {
          hasConfigMap: !!configMap,
          configMapSize: configMap?.size,
          configMapKeys: configMap ? Array.from(configMap.keys()) : []
        });
      }

      // Forzar un pequeño delay y verificar nuevamente
      setTimeout(() => {
        const verificacionTardiaStored = this.configurationsState?.get(operation.operationId);
        const verificacionTardiaFresh = getProjectState(this.projectId!)?.get('productConfigurations')?.get(operation.operationId);

        console.log(`[ConfigurationsCollaboration] ⏰ Verificación tardía (100ms después):`, {
          enMapaAlmacenado: !!verificacionTardiaStored,
          enMapaFresco: !!verificacionTardiaFresh,
          ambosExisten: !!verificacionTardiaStored && !!verificacionTardiaFresh
        });

        if (!verificacionTardiaStored && !verificacionTardiaFresh) {
          console.log(`[ConfigurationsCollaboration] ⚠️ La operación no se persistió en ningún mapa - posible problema de sincronización`);
        } else if (!verificacionTardiaStored) {
          console.log(`[ConfigurationsCollaboration] ⚠️ La operación solo existe en projectState, no en configurationsState - problema de referencia`);
        }
      }, 100);

    } catch (error) {
      console.error(`[ConfigurationsCollaboration] ❌ Error agregando operación a YJS:`, error);
    }
  }

  /**
   * Limpia el estado de colaboración
   */
  cleanup(): void {
    console.log(`[ConfigurationsCollaboration] 🧹 Limpiando estado de colaboración`);
    
    this.projectId = null;
    this.configurationsState = null;
    this.isInitialized = false;
    this.ownOperations.clear();
    this.lastProcessedTimestamp = 0;
  }

  /**
   * Verifica si la colaboración está inicializada
   */
  isCollaborationInitialized(): boolean {
    return this.isInitialized;
  }
}

// Exportar instancia singleton
const configurationsCollaborationService = new ConfigurationsCollaborationService();
export default configurationsCollaborationService;
