import React, { Component } from 'react';
import ProjectService from '../../../Application/Project/ProjectService';
import { getProjectProvider } from '../../../DataProvider/Services/collab/collaborationService';
import {
  setupProductCatalogAwareness,
  onProductCatalogAwarenessChange,
  destroyProductCatalogAwareness
} from '../../../DataProvider/Services/collab/productCatalogAwarenessService';
import './ProductCatalogAwareness.css';

interface Props {
  projectService: ProjectService;
}

interface State {
  connectedUsers: Array<{
    id: string;
    name: string;
    color: string;
    lastActivity: string;
    action?: {
      type: 'viewing_product' | 'editing_product' | 'creating_product' | 'browsing_catalog' | 'idle';
      productId?: string;
      productName?: string;
      timestamp: string;
      details?: {
        modalType?: 'view' | 'edit' | 'create';
        isInModal?: boolean;
      };
    };
  }>;
  isCollaborative: boolean;
  isConnected: boolean;
  totalUsers: number;
  usersInProducts: number;
}

class ProductCatalogAwareness extends Component<Props, State> {
  private awarenessInterval: NodeJS.Timeout | null = null;
  private awarenessUnsubscribe: (() => void) | null = null;
  private currentProjectId: string | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      connectedUsers: [],
      isCollaborative: false,
      isConnected: false,
      totalUsers: 0,
      usersInProducts: 0,
    };
  }

  componentDidMount() {
    console.log('[ProductCatalogAwareness] 🚀 ComponentDidMount - Inicializando...');
    this.updateProjectInfo();
    this.props.projectService.addUpdateProjectListener(this.handleProjectChange.bind(this));
  }

  componentWillUnmount() {
    this.cleanup();
  }

  cleanup = () => {
    console.log('[ProductCatalogAwareness] 🧹 Limpiando recursos...');
    
    if (this.awarenessInterval) {
      clearInterval(this.awarenessInterval);
      this.awarenessInterval = null;
    }

    if (this.awarenessUnsubscribe) {
      this.awarenessUnsubscribe();
      this.awarenessUnsubscribe = null;
    }

    if (this.currentProjectId) {
      destroyProductCatalogAwareness(this.currentProjectId);
    }
  };

  handleProjectChange = () => {
    console.log('[ProductCatalogAwareness] 📡 Proyecto cambiado, actualizando...');
    this.updateProjectInfo();
  };

  updateProjectInfo = () => {
    const projectInfo = this.props.projectService.getProjectInformation();
    const isCollaborative = projectInfo?.collaborative || false;

    console.log('[ProductCatalogAwareness] 📋 Información del proyecto:', {
      hasProjectInfo: !!projectInfo,
      isCollaborative,
      projectId: projectInfo?.project?.id
    });

    this.setState({ isCollaborative });

    if (isCollaborative && projectInfo?.project?.id) {
      const newProjectId = projectInfo.project.id;
      
      // Si cambió el proyecto, limpiar el anterior
      if (this.currentProjectId && this.currentProjectId !== newProjectId) {
        this.cleanup();
      }

      this.currentProjectId = newProjectId;
      this.initializeProductCatalogAwareness(newProjectId);
    } else {
      this.cleanup();
      this.setState({
        isConnected: false,
        connectedUsers: [],
        totalUsers: 0,
        usersInProducts: 0
      });
    }
  };

  initializeProductCatalogAwareness = (projectId: string) => {
    console.log('[ProductCatalogAwareness] 🔧 Inicializando awareness para proyecto:', projectId);

    // Configurar awareness
    const provider = getProjectProvider(projectId);
    const currentUser = this.props.projectService.getUser();
    const collaborators = this.props.projectService.getProjectCollaborators();
    
    const userInfo = collaborators.find((collab: any) => collab.id === currentUser);
    
    if (provider && userInfo) {
      setupProductCatalogAwareness(projectId, provider, {
        name: userInfo.name,
        color: userInfo.color || "#" + Math.floor(Math.random() * 16777215).toString(16)
      });

      // Suscribirse a cambios de awareness
      this.awarenessUnsubscribe = onProductCatalogAwarenessChange(projectId, (states) => {
        this.updateAwarenessState(projectId, states);
      });

      // Iniciar monitoreo periódico
      this.startAwarenessMonitoring(projectId);
    }
  };

  startAwarenessMonitoring = (projectId: string) => {
    // Verificar cada 2 segundos el estado de awareness
    this.awarenessInterval = setInterval(() => {
      const provider = getProjectProvider(projectId);
      if (provider?.awareness) {
        const states = provider.awareness.getStates();
        this.updateAwarenessState(projectId, states);
      }
    }, 2000);
  };

  updateAwarenessState = (projectId: string, awarenessStates: Map<number, any>) => {
    try {
      const provider = getProjectProvider(projectId);
      if (!provider || !provider.awareness) {
        this.setState({
          isConnected: false,
          connectedUsers: []
        });
        return;
      }

      const isConnected = provider.wsconnected || false;
      const currentUserId = this.props.projectService.getUser();
      const collaborators = this.props.projectService.getProjectCollaborators();

      const connectedUsers = Array.from(awarenessStates.entries())
        .map(([clientId, state]) => {
          if (state?.productCatalogUser) {
            // Buscar información del colaborador
            const collaborator = collaborators.find((collab: any) => collab.id !== currentUserId);
            
            if (collaborator) {
              return {
                id: collaborator.id,
                name: state.productCatalogUser.name,
                color: state.productCatalogUser.color || '#6c757d',
                lastActivity: state.productCatalogUser.action?.timestamp || new Date().toISOString(),
                action: state.productCatalogUser.action
              };
            }
          }
          return null;
        })
        .filter(user => user !== null) as Array<{
          id: string;
          name: string;
          color: string;
          lastActivity: string;
          action?: any;
        }>;

      const totalUsers = connectedUsers.length;
      const usersInProducts = connectedUsers.filter(user => 
        user.action?.details?.isInModal || 
        ['viewing_product', 'editing_product', 'creating_product'].includes(user.action?.type)
      ).length;

      this.setState({
        isConnected,
        connectedUsers,
        totalUsers,
        usersInProducts
      });

    } catch (error) {
      console.error('[ProductCatalogAwareness] ❌ Error actualizando estado de awareness:', error);
    }
  };

  formatLastActivity = (timestamp: string): string => {
    try {
      const now = new Date();
      const activityTime = new Date(timestamp);
      const diffMs = now.getTime() - activityTime.getTime();
      const diffSeconds = Math.floor(diffMs / 1000);
      const diffMinutes = Math.floor(diffSeconds / 60);

      if (diffSeconds < 30) return 'now';
      if (diffSeconds < 60) return `${diffSeconds}s ago`;
      if (diffMinutes < 60) return `${diffMinutes}m ago`;
      return `${Math.floor(diffMinutes / 60)}h ago`;
    } catch {
      return 'unknown';
    }
  };

  getActionIcon = (action?: any): string => {
    if (!action) return '🏠';
    
    switch (action.type) {
      case 'viewing_product': return '👁️';
      case 'editing_product': return '✏️';
      case 'creating_product': return '➕';
      case 'browsing_catalog': return '📋';
      case 'idle': return '💤';
      default: return '🏠';
    }
  };

  getActionText = (action?: any): string => {
    if (!action) return 'Browsing';
    
    switch (action.type) {
      case 'viewing_product': return `Viewing: ${action.productName || 'Product'}`;
      case 'editing_product': return `Editing: ${action.productName || 'Product'}`;
      case 'creating_product': return 'Creating new product';
      case 'browsing_catalog': return 'Browsing catalog';
      case 'idle': return 'Idle';
      default: return 'Browsing';
    }
  };

  render() {
    const { isCollaborative, isConnected, connectedUsers, totalUsers, usersInProducts } = this.state;

    // Solo mostrar si el proyecto es colaborativo
    if (!isCollaborative) {
      return null;
    }

    return (
      <div className="product-catalog-awareness">
        <div className="awareness-header">
          <span className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
            {isConnected ? '🟢' : '🔴'}
          </span>
          <span className="awareness-title">
            📋 Product Catalog: {totalUsers} online, {usersInProducts} active
          </span>
        </div>

        {connectedUsers.length > 0 && (
          <div className="connected-users">
            {connectedUsers.map((user) => (
              <div key={user.id} className="connected-user" title={`${user.name} - ${this.getActionText(user.action)} - ${this.formatLastActivity(user.lastActivity)}`}>
                <div className="user-container">
                  <div
                    className="user-avatar"
                    style={{ backgroundColor: user.color }}
                  >
                    {user.name.charAt(0).toUpperCase()}
                    <span className="action-indicator">{this.getActionIcon(user.action)}</span>
                  </div>
                  <div className="user-info">
                    <div className="user-name">{user.name}</div>
                    <div className="user-action">{this.getActionText(user.action)}</div>
                    <div className="user-activity">{this.formatLastActivity(user.lastActivity)}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {connectedUsers.length === 0 && isConnected && (
          <div className="no-users">
            <small className="text-muted">No other users in catalog</small>
          </div>
        )}
      </div>
    );
  }
}

export default ProductCatalogAwareness;
